'use client';

import DashboardLayout from '@/components/layout/dashboard-layout';
import MCPAnalysisResults from '@/components/MCPAnalysisResults';
import RepositoryAnalysisPanel from '@/components/RepositoryAnalysisPanel';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/use-auth';
import { useMCPAnalysis } from '@/hooks/useMCPAnalysis';
import apiClient from '@/lib/api';
import { renderMarkdown } from '@/lib/markdown';
import {
    ArrowLeftIcon,
    ArrowRightOnRectangleIcon,
    ChatBubbleLeftRightIcon,
    CheckCircleIcon,
    CodeBracketIcon,
    CogIcon,
    CpuChipIcon,
    PaperAirplaneIcon,
    SparklesIcon,
    UserIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: MCPSuggestion[];
  showMcpButtons?: boolean;
  serverName?: string;
  requirements?: string;
  showActionButtons?: boolean;
  actionButtons?: string[];
}

interface MCPSuggestion {
  type: 'workflow' | 'custom_mcp' | 'existing_mcp';
  title: string;
  description: string;
  marketplace_url?: string;
}

interface MCPWorkflow {
  workflow_id: string;
  workflow: {
    name: string;
    description: string;
    steps: any[];
    status: string;
  };
}

interface Analysis {
  id: number;
  repo_name: string;
  repo_url: string;
  status: string;
  created_at: string;
  updated_at: string;
  analysis_data?: any;
}

export default function MCPAssistantPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const analysisId = params.id as string;

  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [workflowState, setWorkflowState] = useState<any>(null);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [useEnhancedChat, setUseEnhancedChat] = useState(true);
  const [currentWorkflow, setCurrentWorkflow] = useState<MCPWorkflow | null>(null);
  const [currentAnalysis, setCurrentAnalysis] = useState<any | null>(null);
  const [showWorkflow, setShowWorkflow] = useState(false);

  // MCP Analysis hook
  const { mcpAnalysis, isLoading: mcpLoading, startAnalysis } = useMCPAnalysis(analysisId);

  // Auto-scroll to bottom when new messages are added
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);



  useEffect(() => {
    fetchAnalysis();
    loadWorkflowState();
    initializeConversation();
  }, [analysisId]);

  const fetchAnalysis = async () => {
    try {
      const response = await apiClient.get(`/analysis/${analysisId}`);
      setAnalysis(response.data);

      // MCP analysis is now part of the main workflow, no need to trigger separately
      // The MCP analysis results will be available when the main analysis is complete
    } catch (err: any) {
      console.error('Failed to fetch analysis:', err);
      setError('Failed to load repository analysis');
    }
  };

  const loadWorkflowState = async () => {
    try {
      const response = await apiClient.get(`/analysis/${analysisId}/workflow/state`);
      if (response.data.success) {
        setWorkflowState(response.data.workflow_state);
      }
    } catch (err: any) {
      console.error('Failed to load workflow state:', err);
      // Don't set error for workflow state as it's not critical
    }
  };

  const initializeConversation = () => {
    const welcomeMessage: ChatMessage = {
      id: '1',
      role: 'assistant',
      content: `👋 Hi! I&apos;m your MCP Assistant. I&apos;ll help you understand what MCPs (Model Context Protocols) you can create for your repository.

**What would you like to achieve with MCPs for this repository?**

Tell me your goals and I&apos;ll suggest the best MCP approach for your needs.`,
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  };

  const sendMessage = async () => {
    if (!currentMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: currentMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const userGoals = currentMessage;
    setCurrentMessage('');
    setIsLoading(true);

    try {
      // Prepare conversation history for context
      const conversationHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Choose between enhanced chat and regular chat
      const chatEndpoint = useEnhancedChat
        ? `/analysis/${analysisId}/enhanced-chat`
        : `/analysis/${analysisId}/mcp/chat`;

      const response = await apiClient.post(chatEndpoint, {
        message: userGoals,
        conversation_history: conversationHistory
      });

      if (response.data.success) {
        console.log('Backend response:', response.data);

        // Handle enhanced chat response
        if (useEnhancedChat && response.data.validation_result) {
          setValidationResult(response.data.validation_result);
        }

        // Update workflow state if provided
        if (response.data.workflow_stage) {
          await loadWorkflowState();
        }

        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: response.data.response,
          timestamp: new Date(),
          showMcpButtons: response.data.show_mcp_buttons || false,
          serverName: response.data.server_name,
          requirements: response.data.requirements,
          showActionButtons: response.data.show_action_buttons || false,
          actionButtons: response.data.action_buttons || [],
          suggestions: response.data.suggestions || []
        };

        console.log('Final message object:', assistantMessage);

        setMessages(prev => [...prev, assistantMessage]);

      // Handle MCP server generation
      if (response.data.is_generation && response.data.generated_server) {
        // Auto-download the generated files
        downloadGeneratedFiles(response.data.generated_server.files);
      }
      } else {
        throw new Error(response.data.error || 'Failed to get response');
      }
    } catch (err: any) {
      console.error('Failed to send message:', err);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateMCPServer = async (chatContext: string) => {
    try {
      setIsLoading(true);

      // Create a comprehensive context for MCP server generation
      const generationContext = {
        repository: {
          name: analysis?.repo_name,
          owner: analysis?.repo_owner,
          url: analysis?.repo_url
        },
        analysis_data: analysis?.analysis_results,
        mcp_analysis: mcpAnalysis,
        chat_context: chatContext,
        user_requirements: messages.filter(m => m.role === 'user').map(m => m.content).join('\n')
      };

      const response = await apiClient.post(`/analysis/${analysisId}/mcp/generate-server`, generationContext);

      if (response.data.success) {
        // Create a new message with the generated MCP server
        const generatedMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `🎉 **MCP Server Generated Successfully!**\n\n\`\`\`python\n${response.data.server_code}\n\`\`\`\n\n**Installation Instructions:**\n${response.data.installation_instructions}\n\n**Usage Example:**\n\`\`\`bash\n${response.data.usage_example}\n\`\`\``,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, generatedMessage]);

        // Optionally download the generated files
        if (response.data.files) {
          downloadGeneratedFiles(response.data.files);
        }
      } else {
        throw new Error(response.data.error || 'Failed to generate MCP server');
      }
    } catch (error) {
      console.error('Error generating MCP server:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '❌ Sorry, I encountered an error while generating the MCP server. Please try again or provide more specific requirements.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const validateRequirements = async (requirements: string) => {
    try {
      setIsLoading(true);
      const response = await apiClient.post(`/analysis/${analysisId}/validate-requirements`, {
        requirements
      });

      setValidationResult(response.data);
      await loadWorkflowState();

      return response.data;
    } catch (error) {
      console.error('Requirements validation failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const executeWorkflowAction = async (action: string, parameters: any = {}) => {
    try {
      setIsLoading(true);
      const response = await apiClient.post(`/analysis/${analysisId}/workflow/action`, {
        action,
        parameters
      });

      await loadWorkflowState();
      return response.data;
    } catch (error) {
      console.error('Workflow action failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const generateFromWorkflow = async (targetLanguage: string = 'python') => {
    try {
      setIsLoading(true);
      const response = await apiClient.post(`/analysis/${analysisId}/generate-from-workflow`, {
        target_language: targetLanguage
      });

      if (response.data.success) {
        // Handle successful generation
        const assistantMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `🎉 **MCP Server Generated Successfully!**\n\nYour requirements-driven MCP server has been generated with ${response.data.tools_generated} tools and ${Math.round(response.data.requirements_coverage?.coverage_percentage || 0)}% requirements coverage.`,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Navigate to playground with generated server
        router.push(`/dashboard/analysis/${analysisId}/mcp-playground?generated=true`);
      }

      return response.data;
    } catch (error) {
      console.error('Workflow generation failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const downloadGeneratedFiles = (files: any[]) => {
    files.forEach(file => {
      const blob = new Blob([file.content], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    });
  };

  const handleMcpButtonAction = async (action: 'generate' | 'continue', serverName: string, requirements: string) => {
    try {
      if (action === 'generate') {
        // Build comprehensive context from conversation
        const conversationContext = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');

        // Create detailed prompt with full context
        const detailedPrompt = `Based on our conversation about ${serverName}, create an MCP server with these specifications:

SERVER NAME: ${serverName}

REQUIREMENTS:
${requirements}

CONVERSATION CONTEXT:
${conversationContext}

Please generate a complete, functional MCP server that addresses the specific needs discussed in our conversation.`;

        const encodedPrompt = encodeURIComponent(detailedPrompt);
        router.push(`/dashboard/analysis/${analysisId}/mcp-playground?prompt=${encodedPrompt}&serverName=${encodeURIComponent(serverName)}&requirements=${encodeURIComponent(requirements)}`);
        return;
      }

      setIsLoading(true);

      const response = await apiClient.post(`/analysis/${analysisId}/mcp/button-action`, {
        action,
        server_name: serverName,
        requirements,
        conversation_history: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      });

      if (response.data.success) {
        const assistantMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: response.data.response,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Handle MCP server generation
        if (response.data.is_generation && response.data.generated_server) {
          // Auto-download the generated files
          downloadGeneratedFiles(response.data.generated_server.files);
        }
      } else {
        throw new Error(response.data.error || 'Failed to process action');
      }
    } catch (error) {
      console.error('Error handling MCP button action:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '❌ Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleActionButton = async (actionText: string) => {
    try {
      setIsLoading(true);

      const response = await apiClient.post(`/analysis/${analysisId}/mcp/action-button`, {
        action_text: actionText,
        conversation_history: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      });

      if (response.data.success) {
        const assistantMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: response.data.response,
          timestamp: new Date(),
          showMcpButtons: response.data.show_mcp_buttons || false,
          serverName: response.data.server_name,
          requirements: response.data.requirements,
          showActionButtons: response.data.show_action_buttons || false,
          actionButtons: response.data.action_buttons || [],
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Handle MCP server generation
        if (response.data.is_generation && response.data.generated_server) {
          downloadGeneratedFiles(response.data.generated_server.files);
        }
      } else {
        throw new Error(response.data.error || 'Failed to process action');
      }
    } catch (error) {
      console.error('Error handling action button:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '❌ Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAskQuestion = async (question: string) => {
    try {
      // Add the question as a user message
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: question,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);
      setIsLoading(true);

      // Send the question to the chat API
      const conversationHistory = [...messages, userMessage].map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const response = await apiClient.post(`/analysis/${analysisId}/mcp/chat`, {
        message: question,
        conversation_history: conversationHistory
      });

      if (response.data.success) {
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: response.data.response,
          timestamp: new Date(),
          showMcpButtons: response.data.show_mcp_buttons || false,
          serverName: response.data.server_name,
          requirements: response.data.requirements,
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Handle MCP server generation
        if (response.data.is_generation && response.data.generated_server) {
          downloadGeneratedFiles(response.data.generated_server.files);
        }
      } else {
        throw new Error(response.data.error || 'Failed to get response');
      }
    } catch (error) {
      console.error('Error asking question:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '❌ Sorry, I encountered an error processing your question. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (error) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
            <p className="text-gray-600">{error}</p>
            <Button onClick={() => router.back()} className="mt-4">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Check if repository analysis is complete
  if (analysis && analysis.status !== 'completed') {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-8">
              <CpuChipIcon className="h-16 w-16 mx-auto mb-4 text-blue-600 animate-spin" />
              <h1 className="text-2xl font-bold text-blue-800 mb-4">Repository Analysis in Progress</h1>
              <p className="text-blue-700 mb-6">
                Your repository is currently being analyzed. The MCP Assistant will be available once the analysis is complete.
              </p>
              <div className="space-y-2 text-sm text-blue-600">
                <p>Status: <span className="font-semibold capitalize">{analysis.status}</span></p>
                <p>Repository: <span className="font-semibold">{analysis.repo_name}</span></p>
              </div>
              <div className="mt-6 space-x-4">
                <Button onClick={() => router.push('/dashboard')} variant="outline">
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
                <Button onClick={() => window.location.reload()}>
                  Refresh Status
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <nav className="bg-white border-b border-gray-200">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Left side - Logo and Navigation */}
            <div className="flex items-center space-x-8">
              {/* Logo */}
              <Link href="/dashboard" className="flex items-center space-x-3">
                <img
                  src="/supermcp.png"
                  alt="SuperMCP Logo"
                  className="h-8 w-8 object-contain"
                />
                <span className="text-lg font-semibold text-gray-900">
                  Super<span className="text-primary">MCP</span>
                </span>
              </Link>
            </div>

            {/* Right side - User menu */}
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">Welcome back!</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  localStorage.removeItem('token');
                  window.location.href = '/login';
                }}
                className="text-gray-600 hover:text-gray-800"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                Sign out
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="h-[calc(100vh-4rem)] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-2 border-b bg-white flex-shrink-0">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center space-x-1"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              <span>Back</span>
            </Button>
            <div className="flex items-center space-x-2">
              <SparklesIcon className="h-5 w-5 text-blue-600" />
              <h1 className="text-xl font-bold">MCP Assistant</h1>
              {analysis && (
                <span className="text-sm text-gray-500">
                  • {analysis.repo_name}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Split Layout: 60% Repository Analysis + 40% Chat */}
        <div className="flex flex-1 min-h-0 overflow-hidden">
          {/* Left Panel - Repository Analysis (60%) */}
          <div className="flex flex-col w-3/5 min-h-0 bg-gray-50 overflow-hidden">
            <div className="border-b border-gray-200 px-4 py-3 flex-shrink-0 bg-gray-100">
              <div className="flex items-center space-x-2">
                <CpuChipIcon className="h-4 w-4 text-gray-700" />
                <span className="font-semibold text-gray-800">Repository Analysis</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Live analysis of your repository structure and MCP opportunities
              </p>
            </div>

            <div className="flex-1 overflow-y-auto p-4 min-h-0 bg-gray-50">
              {analysis ? (
                <div className="space-y-6">
                  <RepositoryAnalysisPanel analysis={analysis} />
                  {/* Workflow State Display */}
                  {workflowState && (
                    <div className="bg-white rounded-lg border p-4">
                      <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <CogIcon className="h-5 w-5 mr-2 text-blue-600" />
                        Workflow Status
                      </h2>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Current Stage:</span>
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">
                            {workflowState.current_stage.replace('_', ' ').toUpperCase()}
                          </span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Ready for Generation:</span>
                          <span className={`px-2 py-1 rounded-md text-sm ${
                            workflowState.ready_for_generation
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {workflowState.ready_for_generation ? 'Yes' : 'Not Yet'}
                          </span>
                        </div>

                        <div>
                          <span className="text-sm font-medium text-gray-700">Completed Stages:</span>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {workflowState.completed_stages.map((stage: string) => (
                              <span key={stage} className="px-2 py-1 bg-gray-100 text-gray-700 rounded-md text-xs">
                                {stage.replace('_', ' ')}
                              </span>
                            ))}
                          </div>
                        </div>

                        {workflowState.ready_for_generation && (
                          <button
                            onClick={() => generateFromWorkflow()}
                            disabled={isLoading}
                            className="w-full mt-3 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm font-medium"
                          >
                            {isLoading ? 'Generating...' : 'Generate MCP Server'}
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Validation Results Display */}
                  {validationResult && (
                    <div className="bg-white rounded-lg border p-4">
                      <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <CheckCircleIcon className="h-5 w-5 mr-2 text-green-600" />
                        Requirements Validation
                      </h2>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Feasibility:</span>
                          <span className={`px-2 py-1 rounded-md text-sm ${
                            validationResult.is_feasible
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {validationResult.is_feasible ? 'Feasible' : 'Needs Attention'}
                          </span>
                        </div>

                        {validationResult.feasibility_score && (
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">Score:</span>
                            <span className="text-sm text-gray-600">
                              {Math.round(validationResult.feasibility_score * 100)}%
                            </span>
                          </div>
                        )}

                        {validationResult.missing_capabilities && validationResult.missing_capabilities.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">Missing Capabilities:</span>
                            <ul className="mt-1 text-sm text-red-600 space-y-1">
                              {validationResult.missing_capabilities.map((cap: string, index: number) => (
                                <li key={index} className="flex items-start">
                                  <span className="mr-1">•</span>
                                  <span>{cap}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {validationResult.suggestions && validationResult.suggestions.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">Suggestions:</span>
                            <ul className="mt-1 text-sm text-blue-600 space-y-1">
                              {validationResult.suggestions.map((suggestion: string, index: number) => (
                                <li key={index} className="flex items-start">
                                  <span className="mr-1">•</span>
                                  <span>{suggestion}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {mcpAnalysis && (
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <SparklesIcon className="h-5 w-5 mr-2 text-purple-600" />
                        MCP Analysis Results
                      </h2>

                      <MCPAnalysisResults
                        mcpAnalysis={mcpAnalysis}
                        onAskQuestion={handleAskQuestion}
                      />
                    </div>
                  )}

                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-600">
                  <div className="text-center">
                    <CpuChipIcon className="h-12 w-12 mx-auto mb-4 opacity-60 text-gray-500" />
                    <p className="text-gray-600">Loading repository analysis...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - MCP Conversation (40%) */}
          <div className="flex flex-col w-2/5 min-h-0 bg-white overflow-hidden border-l border-gray-200">

            <div className="border-b border-gray-200 px-4 py-3 flex-shrink-0 bg-blue-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <ChatBubbleLeftRightIcon className="h-4 w-4 text-blue-600" />
                  <span className="font-semibold text-blue-800">MCP Conversation</span>
                </div>

                <div className="flex items-center space-x-2">
                  <label className="text-xs text-blue-700">Enhanced Chat</label>
                  <button
                    onClick={() => setUseEnhancedChat(!useEnhancedChat)}
                    className={`relative inline-flex h-4 w-8 items-center rounded-full transition-colors ${
                      useEnhancedChat ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  >
                    <span
                      className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                        useEnhancedChat ? 'translate-x-4' : 'translate-x-0.5'
                      }`}
                    />
                  </button>
                </div>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                {useEnhancedChat
                  ? "Enhanced chat with requirement validation and workflow integration."
                  : "Tell me what you want to achieve with MCPs, and I'll help you design the perfect solution."
                }
              </p>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-6 space-y-8 min-h-0 bg-white">
              {messages.map((message) => (
                <div key={message.id} className="space-y-4">
                  <div className="flex items-start space-x-4">
                    {message.role === 'assistant' && (
                      <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <CpuChipIcon className="h-4 w-4 text-white" />
                      </div>
                    )}
                    {message.role === 'user' && (
                      <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                        <UserIcon className="h-4 w-4 text-white" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-sm font-semibold text-gray-900">
                          {message.role === 'user' ? 'You' : 'MCP Assistant'}
                        </span>
                        <span className="text-xs text-gray-500">
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <div
                        className="prose prose-sm max-w-none break-words text-gray-700 leading-relaxed"
                        style={{
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word',
                          hyphens: 'auto'
                        }}
                        dangerouslySetInnerHTML={{
                          __html: message.role === 'user'
                            ? message.content.replace(/\n/g, '<br>')
                            : renderMarkdown(message.content)
                        }}
                      />
                    </div>
                  </div>

                  {/* Action Buttons */}
                  {message.role === 'assistant' && message.showActionButtons && message.actionButtons && message.actionButtons.length > 0 && (
                    <div className="ml-12 mt-4">
                      <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                        <div className="text-xs font-medium text-gray-600 mb-3 uppercase tracking-wide">
                          Quick Actions
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {message.actionButtons.map((actionText, index) => (
                            <button
                              key={index}
                              onClick={() => handleActionButton(actionText)}
                              disabled={isLoading}
                              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
                            >
                              <span className="mr-2">⚡</span>
                              {actionText}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* MCP Generation Buttons */}
                  {message.role === 'assistant' && message.showMcpButtons && (
                    <div className="ml-12 mt-4">
                      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-4 border border-purple-200">
                        <div className="text-xs font-medium text-purple-700 mb-3 uppercase tracking-wide">
                          MCP Server Actions
                        </div>
                        <div className="flex flex-wrap gap-3">
                          <button
                            onClick={() => handleMcpButtonAction('generate', message.serverName || '', message.requirements || '')}
                            disabled={isLoading}
                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
                          >
                            <CodeBracketIcon className="h-4 w-4 mr-2" />
                            Generate MCP Server
                          </button>
                          <button
                            onClick={() => handleMcpButtonAction('continue', message.serverName || '', message.requirements || '')}
                            disabled={isLoading}
                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-600 bg-white hover:bg-purple-50 border border-purple-200 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
                          >
                            <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                            Continue Chat
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                </div>
              ))}
              
              {isLoading && (
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <CpuChipIcon className="h-4 w-4 text-white animate-pulse" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-sm font-semibold text-gray-900">MCP Assistant</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      <span>Thinking...</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Scroll target */}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="border-t border-gray-200 p-6 bg-white flex-shrink-0">
              <div className="flex space-x-4">
                <div className="flex-1 relative">
                  <Input
                    value={currentMessage}
                    onChange={(e) => setCurrentMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Ask about MCP..."
                    className="w-full pl-4 pr-12 py-4 text-base border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                    disabled={isLoading}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <span className="text-xs text-gray-400">⌘ + Enter</span>
                  </div>
                </div>
                <Button
                  onClick={sendMessage}
                  disabled={!currentMessage.trim() || isLoading}
                  className="px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-sm transition-all duration-200 hover:shadow-md transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <PaperAirplaneIcon className="h-5 w-5" />
                </Button>
              </div>
              <div className="text-xs text-gray-500 mt-2 text-center">
                Press Enter to send • Shift+Enter for new line
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
