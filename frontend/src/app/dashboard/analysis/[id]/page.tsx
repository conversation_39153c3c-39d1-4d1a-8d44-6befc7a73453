'use client';

import IndexingStatus from '@/components/analysis/IndexingStatus';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { RepositoryTree } from '@/components/ui/repository-tree';
import apiClient, { Analysis, Dependency } from '@/lib/api';
import {
    ArrowLeftIcon,
    ArrowPathIcon,
    ChartBarIcon,
    CheckCircleIcon,
    ClockIcon,
    ExclamationTriangleIcon,
    EyeIcon,
    SparklesIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function AnalysisDetailPage() {
  const params = useParams();
  const router = useRouter();
  const analysisId = parseInt(params.id as string);

  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [dependencies, setDependencies] = useState<Dependency[]>([]);
  const [status, setStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dependencyFilter, setDependencyFilter] = useState<string>('all');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [analysisHealthWarning, setAnalysisHealthWarning] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalysisData();
  }, [analysisId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (analysis && (analysis.status === 'analyzing' || analysis.status === 'pending') && autoRefresh) {
      interval = setInterval(() => {
        fetchAnalysisStatus();
      }, 3000); // Refresh every 3 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [analysis?.status, autoRefresh]);

  const fetchAnalysisData = async () => {
    try {
      const [analysisData, dependenciesData] = await Promise.all([
        apiClient.getAnalysis(analysisId),
        apiClient.getAnalysisDependencies(analysisId)
      ]);

      setAnalysis(analysisData);
      setDependencies(dependenciesData);

      // Fetch status if analysis is in progress
      if (analysisData.status === 'analyzing' || analysisData.status === 'pending') {
        fetchAnalysisStatus();
      }
    } catch (err) {
      console.error('Failed to fetch analysis data:', err);
      setError('Failed to load analysis data');
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalysisStatus = async () => {
    try {
      const statusData = await apiClient.getAnalysisStatus(analysisId);
      setStatus(statusData);

      // Check for stuck analysis
      if (analysis && (analysis.status === 'analyzing' || analysis.status === 'pending')) {
        const now = Date.now();
        const startTime = new Date(analysis.created_at).getTime();
        const elapsed = now - startTime;

        // Warn if analysis is taking too long
        if (elapsed > 300000) { // 5 minutes
          setAnalysisHealthWarning('Analysis is taking longer than expected. This could be due to repository size or network issues.');
        } else if (elapsed > 600000) { // 10 minutes
          setAnalysisHealthWarning('Analysis may be stuck. Consider refreshing the page or contacting support if this persists.');
        } else {
          setAnalysisHealthWarning(null);
        }
      } else {
        setAnalysisHealthWarning(null);
      }

      // If status changed to completed/failed, refresh full data
      if (statusData.status !== analysis?.status &&
          (statusData.status === 'completed' || statusData.status === 'failed')) {
        setAutoRefresh(false);
        fetchAnalysisData();
      }
    } catch (err: any) {
      console.error('Failed to fetch analysis status:', err);

      // For timeout errors during large repository processing, don't show error
      // The analysis might still be running successfully
      if (err?.code === 'ECONNABORTED' || err?.message?.includes('timeout')) {
        console.log('Status request timed out - large repository analysis may still be in progress');
      }
    }
  };

  const handleRetry = async () => {
    try {
      await apiClient.retryAnalysis(analysisId);
      setAutoRefresh(true);
      setTimeout(() => fetchAnalysisData(), 1000);
    } catch (err) {
      console.error('Failed to retry analysis:', err);
    }
  };

  const handleExport = async (format: 'json' | 'csv') => {
    try {
      const data = await apiClient.exportAnalysis(analysisId, format);

      // Create and download file
      const blob = new Blob([format === 'json' ? JSON.stringify(data, null, 2) : data.csv_data], {
        type: format === 'json' ? 'application/json' : 'text/csv'
      });

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analysis-${analysisId}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export analysis:', err);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'analyzing':
        return <ClockIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'success',
      failed: 'destructive',
      analyzing: 'default',
      pending: 'secondary'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    );
  };

  const getMCPScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 75) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressPercentage = () => {
    if (!status || !status.task_info) {
      // If no task info available, show minimal progress to indicate activity
      if (analysis.status === 'analyzing') {
        return 10; // Show minimal progress to indicate analysis has started
      } else if (analysis.status === 'pending') {
        return 5; // Show very minimal progress for pending
      }
      return 0;
    }

    // Use actual task progress when available
    const { current = 0, total = 100 } = status.task_info;
    const realProgress = Math.round((current / total) * 100);

    // Ensure progress is at least 5% if analysis is running
    if (analysis.status === 'analyzing' && realProgress < 5) {
      return 5;
    }

    return realProgress;
  };

  const filteredDependencies = dependencies.filter(dep => {
    if (dependencyFilter === 'all') return true;
    if (dependencyFilter === 'high-mcp') return (dep.mcp_potential || 0) >= 0.7;
    if (dependencyFilter === 'medium-mcp') return (dep.mcp_potential || 0) >= 0.4 && (dep.mcp_potential || 0) < 0.7;
    if (dependencyFilter === 'low-mcp') return (dep.mcp_potential || 0) < 0.4;
    return dep.language === dependencyFilter;
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !analysis) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <XCircleIcon className="mx-auto h-12 w-12 text-red-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
          <p className="mt-1 text-sm text-gray-500">{error || 'Analysis not found'}</p>
          <div className="mt-6">
            <Link href="/dashboard/analysis">
              <Button>Back to Analyses</Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/analysis">
              <Button variant="outline" size="sm">
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                {getStatusIcon(analysis.status)}
                <span className="ml-2">{analysis.repo_owner}/{analysis.repo_name}</span>
              </h1>
              <p className="text-sm text-gray-600">
                Analysis started {new Date(analysis.created_at).toLocaleString()}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {getStatusBadge(analysis.status)}

            {analysis.status === 'failed' && (
              <Button size="sm" onClick={handleRetry} className="border-orange-200 text-orange-600 hover:bg-orange-50">
                <ArrowPathIcon className="mr-2 h-4 w-4" />
                Retry
              </Button>
            )}

            {analysis.status === 'completed' && (
              <Link href={`/dashboard/analysis/${analysisId}/mcp-assistant`}>
                <Button size="sm">
                  <SparklesIcon className="mr-2 h-4 w-4" />
                  MCP Assistant
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Progress Section */}
        {(analysis.status === 'analyzing' || analysis.status === 'pending') && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ClockIcon className="mr-2 h-5 w-5 animate-spin" />
                Analysis in Progress
              </CardTitle>
              <CardDescription>
                {status?.task_info?.status || (() => {
                  // Provide more detailed status based on elapsed time when no task info
                  const now = Date.now();
                  const startTime = new Date(analysis.created_at).getTime();
                  const elapsed = now - startTime;

                  if (elapsed < 30000) return 'Starting repository analysis...';
                  if (elapsed < 60000) return 'Fetching repository data...';
                  if (elapsed < 120000) return 'Analyzing code structure...';
                  if (elapsed < 180000) return 'Processing business logic...';
                  return 'Generating MCP suggestions...';
                })()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Progress value={getProgressPercentage()} className="w-full" />
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Progress: {getProgressPercentage()}%</span>
                  {status?.task_info ? (
                    <span>{status.task_info.current || 0} / {status.task_info.total || 100}</span>
                  ) : (
                    <span>Analyzing repository...</span>
                  )}
                </div>

                {/* Current Analysis Details */}
                {status?.task_info?.details && (
                  <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
                    <div className="text-sm font-medium text-blue-800">Currently Processing</div>
                    <div className="text-sm text-blue-700 mt-1">{status.task_info.details}</div>
                    {status.task_info.current_task && (
                      <div className="text-xs text-blue-600 mt-1">
                        Task: {status.task_info.current_task.replace('_', ' ').toUpperCase()}
                      </div>
                    )}
                  </div>
                )}

                {/* Health Warning */}
                {analysisHealthWarning && (
                  <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                    <div className="flex items-start">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="text-sm font-medium text-yellow-800">Large Repository Processing</div>
                        <div className="text-sm text-yellow-700 mt-1">{analysisHealthWarning}</div>
                        <div className="text-xs text-yellow-600 mt-2">
                          Large repositories with many files and integrations may take 5-10 minutes to analyze completely.
                          The analysis continues in the background even if status requests timeout.
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {autoRefresh && (
                  <p className="text-xs text-gray-500">
                    Auto-refreshing every 3 seconds...
                  </p>
                )}

                {/* Show additional info if task seems stuck */}
                {analysis.status === 'analyzing' &&
                 (!status?.task_info || getProgressPercentage() === 0) && (
                  <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
                    <p className="font-medium">Analysis may be taking longer than expected.</p>
                    <p className="text-xs mt-1">
                      This could be due to repository size, network issues, or backend processing.
                      The analysis will continue in the background.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Message */}
        {analysis.error_message && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex">
                <XCircleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Analysis Failed</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{analysis.error_message}</p>
                  </div>

                  {/* Show technical details if available */}
                  {analysis.error_message.includes('JSON serializable') && (
                    <div className="mt-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                      <div className="text-sm font-medium text-yellow-800">Technical Issue Detected</div>
                      <div className="text-sm text-yellow-700 mt-1">
                        This appears to be a data serialization issue. The analysis may have completed but failed to save properly.
                        Retrying should resolve this issue.
                      </div>
                    </div>
                  )}

                  <div className="mt-4">
                    <Button onClick={handleRetry} className="bg-red-600 hover:bg-red-700">
                      <ArrowPathIcon className="mr-2 h-4 w-4" />
                      Retry Analysis
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* MCP Feasibility Score removed - replaced by conversational MCP assistant */}

        {/* Indexing Status */}
        {(analysis.status === 'analyzing' || analysis.status === 'completed') && (
          <IndexingStatus
            analysisId={analysisId}
            analysisStatus={analysis.status}
            autoRefresh={autoRefresh}
          />
        )}

        {/* Technology Stack Analysis */}
        {analysis.status === 'completed' && (
          <div className="space-y-6">
            {/* Tech Stack Panel will be loaded here */}
            <Card className="border-0 bg-gradient-to-br from-white via-white to-blue-50/30 shadow-lg shadow-blue-100/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
                  <ChartBarIcon className="mr-3 h-6 w-6 text-blue-600" />
                  Technology Stack Analysis
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Comprehensive analysis of detected technologies with confidence scoring
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  {/* Primary Language */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                      Primary Language
                    </h4>
                    <div className="p-4 rounded-lg bg-white border border-gray-100">
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-medium text-gray-900">
                          {(() => {
                            // FIXED: Try enhanced results first, then fallback to old path
                            const enhancedTechStack = analysis.intermediate_data?.enhanced_results?.technology_stack;
                            if (enhancedTechStack?.primary_language) {
                              return enhancedTechStack.primary_language;
                            }

                            // Fallback to old logic for backward compatibility
                            const repoInfo = analysis.analysis_results?.repository_info;
                            const primaryLang = repoInfo?.language;

                            // If no primary language, get the highest percentage language
                            if (!primaryLang && repoInfo?.language_percentages) {
                              const percentages = repoInfo.language_percentages;
                              const topLang = Object.entries(percentages)
                                .sort(([,a], [,b]) => (b as number) - (a as number))[0];
                              return topLang ? topLang[0] : 'Unknown';
                            }

                            return primaryLang || 'Unknown';
                          })()}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {(() => {
                            // Check enhanced results first
                            const enhancedTechStack = analysis.intermediate_data?.enhanced_results?.technology_stack;
                            if (enhancedTechStack?.primary_language) {
                              return 'Enhanced Analysis';
                            }

                            // Fallback to old logic
                            const repoInfo = analysis.analysis_results?.repository_info;
                            const hasLanguage = repoInfo?.language || repoInfo?.language_percentages;
                            return hasLanguage ? 'GitHub Verified' : 'Not Detected';
                          })()}
                        </Badge>
                      </div>

                      {/* Language Percentages */}
                      {(() => {
                        // Try enhanced results first
                        const enhancedTechStack = analysis.intermediate_data?.enhanced_results?.technology_stack;
                        const languages = enhancedTechStack?.languages;

                        if (languages && Object.keys(languages).length > 0) {
                          return (
                            <div className="mt-3 space-y-2">
                              {Object.entries(languages)
                                .sort(([,a], [,b]) => (b as number) - (a as number))
                                .slice(0, 4)
                                .map(([lang, percentage]) => (
                                <div key={lang} className="flex items-center justify-between text-sm">
                                  <span className="text-gray-700">{lang}</span>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-16 bg-gray-200 rounded-full h-2">
                                      <div
                                        className="bg-blue-500 h-2 rounded-full"
                                        style={{ width: `${percentage}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-gray-600 text-xs w-10 text-right">{percentage}%</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          );
                        }

                        // Fallback to old logic
                        const languagePercentages = analysis.analysis_results?.repository_info?.language_percentages;
                        if (languagePercentages && Object.keys(languagePercentages).length > 0) {
                          return (
                            <div className="mt-3 space-y-2">
                              {Object.entries(languagePercentages)
                                .sort(([,a], [,b]) => (b as number) - (a as number))
                                .slice(0, 4)
                                .map(([lang, percentage]) => (
                                <div key={lang} className="flex items-center justify-between text-sm">
                                  <span className="text-gray-700">{lang}</span>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-16 bg-gray-200 rounded-full h-2">
                                      <div
                                        className="bg-blue-500 h-2 rounded-full"
                                        style={{ width: `${percentage}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-gray-600 text-xs w-10 text-right">{percentage}%</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          );
                        }

                        return null;
                      })()}
                    </div>
                  </div>

                  {/* Dependencies Count */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Dependencies
                    </h4>
                    <div className="p-4 rounded-lg bg-white border border-gray-100">
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-medium text-gray-900">
                          {dependencies.length} detected
                        </span>
                        <Badge variant="outline" className={`text-xs ${dependencies.length > 0 ? 'text-green-700 bg-green-50' : 'text-gray-600'}`}>
                          {dependencies.length > 0 ? 'Analyzed' : 'None Found'}
                        </Badge>
                      </div>

                      {dependencies.length === 0 && (
                        <div className="mt-2 text-sm text-gray-500">
                          No dependency files detected. This may be a library, documentation, or configuration repository.
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Dependencies List */}
                {dependencies && dependencies.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Dependencies & Libraries</h4>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {dependencies.slice(0, 10).map((dep) => (
                        <div key={dep.id} className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                          <div className="flex items-center space-x-3">
                            <Badge variant="outline" className="text-xs">
                              {dep.language}
                            </Badge>
                            <span className="font-medium text-gray-900">{dep.name}</span>
                            {dep.version && (
                              <span className="text-sm text-gray-500">v{dep.version}</span>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              className={`text-xs ${
                                dep.mcp_potential >= 0.7
                                  ? 'bg-green-100 text-green-800'
                                  : dep.mcp_potential >= 0.4
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {Math.round(dep.mcp_potential * 100)}% MCP Potential
                            </Badge>
                          </div>
                        </div>
                      ))}

                      {dependencies.length > 10 && (
                        <div className="text-center pt-2">
                          <span className="text-sm text-gray-500">
                            ... and {dependencies.length - 10} more dependencies
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Repository Structure */}
        {analysis.status === 'completed' && (
          <Card className="border-0 bg-gradient-to-br from-white via-white to-gray-50/30 shadow-lg shadow-gray-100/50">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
                <ChartBarIcon className="mr-3 h-6 w-6 text-indigo-600" />
                Repository Structure
              </CardTitle>
              <CardDescription className="text-gray-600">
                Detailed analysis of repository architecture and components
              </CardDescription>
            </CardHeader>
            <CardContent>
              {(() => {
                // FIXED: Read from consolidated analysis_results (where backend now stores data)
                const analysisResults = analysis.analysis_results || {};
                const techStack = analysisResults.technology_stack || {};
                const repoInfo = analysisResults.repository_info || {};
                const repoStructure = repoInfo.repository_structure || techStack.repository_structure;
                const languages = enhancedTechStack?.languages;

                // Check if we have enhanced repository structure data
                if (repoStructure && Object.keys(repoStructure).length > 0) {
                  return (
                    <div className="grid gap-6 md:grid-cols-2">
                      <div className="space-y-4">
                        <h4 className="font-semibold text-gray-900 flex items-center">
                          <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                          Repository Overview
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100">
                            <span className="font-medium text-gray-700">Total Files</span>
                            <span className="text-sm font-semibold text-gray-900">{repoStructure.total_files || 0}</span>
                          </div>
                          <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100">
                            <span className="font-medium text-gray-700">Directories</span>
                            <span className="text-sm font-semibold text-gray-900">{repoStructure.directories?.length || 0}</span>
                          </div>
                          <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100">
                            <span className="font-medium text-gray-700">Key Files</span>
                            <span className="text-sm font-semibold text-gray-900">{repoStructure.key_files?.length || 0}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-semibold text-gray-900 flex items-center">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                          Key Files Found
                        </h4>
                        <div className="space-y-2 max-h-48 overflow-y-auto">
                          {repoStructure.key_files?.slice(0, 10).map((file: string, index: number) => (
                            <div key={index} className="flex items-center p-2 rounded bg-white border border-gray-100">
                              <span className="text-sm text-gray-700">{file}</span>
                            </div>
                          ))}
                          {repoStructure.key_files?.length > 10 && (
                            <div className="text-xs text-gray-500 text-center">
                              ... and {repoStructure.key_files.length - 10} more files
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                }

                // Fallback to old repository tree logic
                if (analysis.analysis_results?.repository_tree) {
                  return (
                    <RepositoryTree
                      tree={analysis.analysis_results.repository_tree}
                      indicators={analysis.analysis_results.repository_tree.mcp_indicators}
                    />
                  );
                }

                // Fallback to old code structure logic
                if (analysis.analysis_results?.code_structure) {
                  return (
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center">
                      <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                      Languages & Files
                    </h4>
                    <div className="space-y-3">
                      {Object.entries(analysis.analysis_results.code_structure.languages || {}).map(([lang, bytes]) => (
                        <div key={lang} className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                          <span className="font-medium text-gray-700">{lang}</span>
                          <div className="text-right">
                            <span className="text-sm font-semibold text-gray-900">{((bytes as number) / 1024).toFixed(1)} KB</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Structure Features
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                        <span className="text-gray-700">Command Line Interface</span>
                        <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          analysis.analysis_results.code_structure.has_cli
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {analysis.analysis_results.code_structure.has_cli ? '✓ Available' : '✗ Not Available'}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                        <span className="text-gray-700">API Endpoints</span>
                        <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          analysis.analysis_results.code_structure.has_api
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {analysis.analysis_results.code_structure.has_api ? '✓ Available' : '✗ Not Available'}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                        <span className="text-gray-700">Database Integration</span>
                        <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          analysis.analysis_results.code_structure.has_database
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {analysis.analysis_results.code_structure.has_database ? '✓ Available' : '✗ Not Available'}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg bg-indigo-50 border border-indigo-100">
                        <span className="text-indigo-700 font-medium">Total Files</span>
                        <div className="px-3 py-1 rounded-full text-sm font-bold bg-indigo-100 text-indigo-800">
                          {analysis.analysis_results.code_structure.file_count || 0}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                  );
                }

                // No data available
                return (
                  <div className="text-center py-12">
                    <EyeIcon className="mx-auto h-16 w-16 text-gray-300" />
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Repository structure not available</h3>
                    <p className="mt-2 text-gray-500 max-w-md mx-auto">
                      The repository structure could not be loaded or analyzed. This may be due to access restrictions or analysis limitations.
                    </p>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}