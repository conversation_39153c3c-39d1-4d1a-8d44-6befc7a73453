'use client';

import { useEffect, useState } from 'react';

interface MCPCapability {
  capability: string;
  underlying_tech: string;
  exposed_via_api: string;
  candidate_tool_name: string;
}

interface MCPServerSpec {
  name: string;
  description: string;
  parameters: Array<{
    name: string;
    type: string;
    required: boolean;
  }>;
}

interface ExistingMCPServer {
  server_name: string;
  overlapping_tools: string;
  when_to_reuse: string;
}

interface MCPAnalysisData {
  executive_summary: string;
  capability_matrix: MCPCapability[];
  new_mcp_server_specs: MCPServerSpec[];
  existing_mcp_servers: ExistingMCPServer[];
  gap_analysis: string;
  implementation_starter: string;
  client_config_snippet: string;
}

interface UseMCPAnalysisResult {
  mcpAnalysis: MCPAnalysisData | null;
  isLoading: boolean;
  error: string | null;
  startAnalysis: (repoUrl: string) => Promise<void>;
  refetch: () => Promise<void>;
}

export function useMCPAnalysis(analysisId: string): UseMCPAnalysisResult {
  const [mcpAnalysis, setMcpAnalysis] = useState<MCPAnalysisData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMCPAnalysis = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use the full backend URL
      const response = await fetch(`http://localhost:8000/api/v1/analysis/${analysisId}/mcp`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch MCP analysis');
      }

      const result = await response.json();

      if (result.success && result.data) {
        setMcpAnalysis(result.data);
      } else {
        throw new Error(result.error || 'Failed to get MCP analysis');
      }
    } catch (err) {
      console.error('Error fetching MCP analysis:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const startAnalysis = async (repoUrl: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/v1/analysis/${analysisId}/mcp/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ repo_url: repoUrl }),
      });

      if (!response.ok) {
        throw new Error('Failed to start MCP analysis');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to start MCP analysis');
      }

      // Start polling for results
      setTimeout(() => {
        fetchMCPAnalysis();
      }, 2000);
    } catch (err) {
      console.error('Error starting MCP analysis:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setIsLoading(false);
    }
  };

  const refetch = async () => {
    await fetchMCPAnalysis();
  };

  // Auto-fetch on mount
  useEffect(() => {
    fetchMCPAnalysis();
  }, [analysisId]);

  // Set up polling when analysis is in progress
  useEffect(() => {
    let pollInterval: NodeJS.Timeout | null = null;

    if (typeof mcpAnalysis?.executive_summary === 'string' && mcpAnalysis.executive_summary.includes('in progress')) {
      // Start polling every 5 seconds when analysis is in progress
      pollInterval = setInterval(() => {
        fetchMCPAnalysis();
      }, 5000);
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [mcpAnalysis?.executive_summary]); // Re-run when executive_summary changes

  return {
    mcpAnalysis,
    isLoading,
    error,
    startAnalysis,
    refetch,
  };
}
