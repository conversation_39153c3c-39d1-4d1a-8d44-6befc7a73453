'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    CheckCircleIcon,
    CodeBracketIcon,
    CpuChipIcon,
    ExclamationTriangleIcon,
    EyeIcon,
    PencilIcon,
    ShieldCheckIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';
import { useState } from 'react';

interface Dependency {
  id: number;
  name: string;
  version?: string;
  language: string;
  dependency_type: string;
  file_path: string;
  mcp_potential: number;
  confidence_score?: number;
  detection_method?: string;
}

interface TechStackInfo {
  primary_language: string;
  languages: Record<string, number>;
  frameworks: string[];
  dependencies: Dependency[];
  confidence_score: number;
  detection_methods: string[];
  last_validated?: string;
}

interface TechStackPanelProps {
  analysis: any;
  dependencies: Dependency[];
  onValidate?: (techStack: TechStackInfo) => void;
  onCorrect?: (corrections: any) => void;
}

export default function TechStackPanel({ 
  analysis, 
  dependencies, 
  onValidate, 
  onCorrect 
}: TechStackPanelProps) {
  const [showValidation, setShowValidation] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'pending' | 'validated' | 'needs_correction'>('pending');

  // Extract tech stack information from analysis
  const extractTechStack = (): TechStackInfo => {
    // FIXED: Read from consolidated analysis_results (where backend now stores data)
    const results = analysis?.analysis_results || {};
    const techStack = results.technology_stack || {};
    const repoInfo = results.repository_info || {};

    // DEBUG: Log what we're getting
    console.log('🔍 TechStack DEBUG: results keys:', Object.keys(results));
    console.log('🔍 TechStack DEBUG: techStack keys:', Object.keys(techStack));
    console.log('🔍 TechStack DEBUG: techStack.primary_language:', techStack.primary_language);
    console.log('🔍 TechStack DEBUG: repoInfo.language:', repoInfo.language);

    // Fallback to intermediate_data for backward compatibility
    const enhancedResults = analysis?.intermediate_data?.enhanced_results || {};
    const fallbackTechStack = enhancedResults.technology_stack || {};
    const codeStructure = results.code_structure || {};

    return {
      primary_language: techStack.primary_language || fallbackTechStack.primary_language || repoInfo.language || 'Unknown',
      languages: techStack.languages || techStack.language_percentages || fallbackTechStack.languages || codeStructure.languages || {},
      frameworks: techStack.frameworks || fallbackTechStack.frameworks || results.frameworks || [],
      dependencies: dependencies || [],
      confidence_score: calculateConfidenceScore(),
      detection_methods: getDetectionMethods(),
      last_validated: results.last_validated
    };
  };

  const calculateConfidenceScore = (): number => {
    // Calculate confidence based on multiple factors
    let score = 0;
    const factors = [];

    // Primary language confidence
    if (analysis?.analysis_results?.repository_info?.language) {
      score += 30;
      factors.push('GitHub API language detection');
    }

    // Dependencies confidence
    if (dependencies && dependencies.length > 0) {
      score += 25;
      factors.push('Dependency file analysis');
    }

    // Code structure confidence
    if (analysis?.analysis_results?.code_structure?.languages) {
      score += 20;
      factors.push('Code structure analysis');
    }

    // File extension analysis
    if (analysis?.analysis_results?.repository_tree) {
      score += 15;
      factors.push('File extension analysis');
    }

    // Framework detection
    if (analysis?.analysis_results?.frameworks?.length > 0) {
      score += 10;
      factors.push('Framework pattern detection');
    }

    return Math.min(score, 100);
  };

  const getDetectionMethods = (): string[] => {
    const methods = [];
    
    if (analysis?.analysis_results?.repository_info?.language) {
      methods.push('GitHub API');
    }
    if (dependencies?.length > 0) {
      methods.push('Dependency Files');
    }
    if (analysis?.analysis_results?.code_structure) {
      methods.push('Code Analysis');
    }
    if (analysis?.analysis_results?.repository_tree) {
      methods.push('File Structure');
    }

    return methods;
  };

  const techStack = extractTechStack();

  const getConfidenceColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getConfidenceIcon = (score: number) => {
    if (score >= 80) return <CheckCircleIcon className="h-4 w-4" />;
    if (score >= 60) return <ExclamationTriangleIcon className="h-4 w-4" />;
    return <XCircleIcon className="h-4 w-4" />;
  };

  const handleValidate = () => {
    setValidationStatus('validated');
    if (onValidate) {
      onValidate(techStack);
    }
  };

  const handleCorrect = () => {
    setShowValidation(true);
    // Open correction interface
  };

  return (
    <div className="space-y-6">
      {/* Tech Stack Overview */}
      <Card className="border-0 bg-gradient-to-br from-white via-white to-blue-50/30 shadow-lg shadow-blue-100/50">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900 flex items-center justify-between">
            <div className="flex items-center">
              <CpuChipIcon className="mr-3 h-6 w-6 text-blue-600" />
              Technology Stack Analysis
            </div>
            <div className="flex items-center space-x-2">
              <Badge className={`${getConfidenceColor(techStack.confidence_score)} border-0`}>
                {getConfidenceIcon(techStack.confidence_score)}
                <span className="ml-1">{techStack.confidence_score}% Confidence</span>
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            {/* Primary Language */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900 flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Primary Language
              </h4>
              <div className="p-4 rounded-lg bg-white border border-gray-100">
                <div className="flex items-center justify-between">
                  <span className="text-lg font-medium text-gray-900">{techStack.primary_language}</span>
                  <Badge variant="outline" className="text-xs">
                    {techStack.detection_methods.includes('GitHub API') ? 'GitHub Verified' : 'Detected'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Language Distribution */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900 flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Language Distribution
              </h4>
              <div className="space-y-2">
                {Object.entries(techStack.languages).map(([lang, bytes]) => (
                  <div key={lang} className="flex items-center justify-between p-2 rounded bg-white border border-gray-100">
                    <span className="font-medium text-gray-700">{lang}</span>
                    <span className="text-sm text-gray-600">{((bytes as number) / 1024).toFixed(1)} KB</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dependencies */}
      {dependencies && dependencies.length > 0 && (
        <Card className="border-0 bg-gradient-to-br from-white via-white to-purple-50/30 shadow-lg shadow-purple-100/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
              <CodeBracketIcon className="mr-3 h-6 w-6 text-purple-600" />
              Dependencies & Frameworks
              <Badge variant="outline" className="ml-2 text-xs">
                {dependencies.length} detected
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dependencies.slice(0, 10).map((dep) => (
                <div key={dep.id} className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline" className="text-xs">
                      {dep.language}
                    </Badge>
                    <span className="font-medium text-gray-900">{dep.name}</span>
                    {dep.version && (
                      <span className="text-sm text-gray-500">v{dep.version}</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      className={`text-xs ${
                        dep.mcp_potential >= 0.7 
                          ? 'bg-green-100 text-green-800' 
                          : dep.mcp_potential >= 0.4 
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {Math.round(dep.mcp_potential * 100)}% MCP Potential
                    </Badge>
                    <span className="text-xs text-gray-500">{dep.file_path}</span>
                  </div>
                </div>
              ))}
              
              {dependencies.length > 10 && (
                <div className="text-center pt-2">
                  <Button variant="outline" size="sm">
                    <EyeIcon className="mr-2 h-4 w-4" />
                    View All {dependencies.length} Dependencies
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detection Methods & Validation */}
      <Card className="border-0 bg-gradient-to-br from-white via-white to-gray-50/30 shadow-lg shadow-gray-100/50">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
            <ShieldCheckIcon className="mr-3 h-6 w-6 text-gray-600" />
            Analysis Validation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Detection Methods */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Detection Methods Used:</h4>
              <div className="flex flex-wrap gap-2">
                {techStack.detection_methods.map((method) => (
                  <Badge key={method} variant="outline" className="text-xs">
                    {method}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Validation Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  Is this tech stack analysis accurate?
                </span>
                {validationStatus === 'validated' && (
                  <Badge className="bg-green-100 text-green-800 border-0">
                    <CheckCircleIcon className="h-3 w-3 mr-1" />
                    Validated
                  </Badge>
                )}
              </div>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleCorrect}
                  disabled={validationStatus === 'validated'}
                >
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Correct
                </Button>
                <Button 
                  size="sm"
                  onClick={handleValidate}
                  disabled={validationStatus === 'validated'}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircleIcon className="mr-2 h-4 w-4" />
                  Validate
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
