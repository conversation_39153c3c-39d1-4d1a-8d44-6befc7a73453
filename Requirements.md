The Idea of this project is to analyse any repo given by user and 
1. Analyze the repo by downloading the github repo to local
2. Index the code and chunk the data and store them in weaviate store
3. The indexing code need to run in parallel to make it fast during repo analysis.
4. Once we have the indexed code, let's pass on this indexed code to enhanced analysis and generate the data.
5. enhanced analysis also should run parallel.
6. Once we have the enhanced analysis results and indexed data, we need to run mcp analysis
7. mcp analysis also should run in parallel.
8. But, Indexing code -> Enhanced analysis -> mcp analysis should run in sequence.
9. For tech stack, dependencies and repository structure, we need to use GitHub APIS.
10. Once we have all this data in place, user should be able to see repo analysis and mcp analysis and can chat on mcp conversation window. 
11. MCP conversation, MCP analysis, Repo analysis should be repo context aware.
12. No fallbacks or mock data should be used across the application.
13. Once the user is ready to build the mcp server, it should hand off the mcp playground and this playground should be able to build the repo context aware fully functional mcp server and test it and if all the tests are passed, then user should be able to download the mcp server code.
This is the complete idea behind this application.